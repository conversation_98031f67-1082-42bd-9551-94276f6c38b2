package com.yjsoft.roadtravel.basiclibrary.network

import android.content.Context
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.yjsoft.roadtravel.basiclibrary.network.cache.CacheInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.cache.NetworkCache
import com.yjsoft.roadtravel.basiclibrary.network.cache.NetworkCacheInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.AuthInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.CommonParamsInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.DefaultErrorHandler
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.ErrorInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.LoggingInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.LoginStatusHandler
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenProvider
import com.yjsoft.roadtravel.basiclibrary.network.utils.RetryInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.utils.RetryPolicy
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

/**
 * 优化的Retrofit实例管理器
 * 集成了认证、错误处理、缓存、重试等功能
 */
class RetrofitInstance private constructor(
    private val context: Context,
    private val tokenProvider: TokenProvider? = null
) {

    // Gson配置
    private val gsonInstance: Gson by lazy {
        GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .serializeNulls()
            .create()
    }

    // 网络缓存管理器
    private val networkCacheInstance: NetworkCache by lazy {
        NetworkCache(context)
    }

    // 登录状态处理器
    private val loginStatusHandler: LoginStatusHandler by lazy {
        LoginStatusHandler(context)
    }

    // OkHttp客户端
    private val okHttpClientInstance: OkHttpClient by lazy {
        OkHttpClient.Builder()
            .apply {
                // 超时配置
                connectTimeout(NetworkConfig.getConnectTimeout(), NetworkConfig.getTimeoutUnit())
                readTimeout(NetworkConfig.getReadTimeout(), NetworkConfig.getTimeoutUnit())
                writeTimeout(NetworkConfig.getWriteTimeout(), NetworkConfig.getTimeoutUnit())

                // 缓存配置
                cache(networkCacheInstance.getCache())

                // 添加拦截器（顺序很重要）
                // 1. 公共参数拦截器（最先执行，为所有请求添加公共参数）
                addInterceptor(CommonParamsInterceptor(context))

                // 2. 应用级拦截器
                addInterceptor(CacheInterceptor())

                // 3. 认证拦截器
                tokenProvider?.let { provider ->
                    addInterceptor(AuthInterceptor(context, provider))
                }

                // 4. 日志拦截器
                addInterceptor(LoggingInterceptor.create())

                // 5. 网络级拦截器
                addNetworkInterceptor(NetworkCacheInterceptor())

                // 6. 重试拦截器
                addInterceptor(RetryInterceptor(RetryPolicy.default()))

                // 7. 错误处理拦截器
                addInterceptor(ErrorInterceptor(gsonInstance, DefaultErrorHandler(), loginStatusHandler))
            }
            .build()
    }

    // Retrofit实例
    private val retrofitInstance: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(NetworkConfig.getBaseUrl())
            .client(okHttpClientInstance)
            .addConverterFactory(GsonConverterFactory.create(gsonInstance))
            .build()
    }

    // API服务实例
    val api: ApiService by lazy {
        retrofitInstance.create(ApiService::class.java)
    }

    /**
     * 获取网络缓存管理器
     */
    fun getNetworkCache(): NetworkCache = networkCacheInstance

    /**
     * 获取Gson实例
     */
    fun getGson(): Gson = gsonInstance

    /**
     * 获取OkHttp客户端
     */
    fun getOkHttpClient(): OkHttpClient = okHttpClientInstance

    /**
     * 获取Retrofit实例
     */
    fun getRetrofit(): Retrofit = retrofitInstance

    /**
     * 创建自定义API服务
     */
    fun <T> createService(serviceClass: Class<T>): T {
        return retrofitInstance.create(serviceClass)
    }

    /**
     * 预热网络组件
     * 提前初始化关键组件以减少首次请求延迟
     */
    fun warmUp() {
        try {
            // 触发懒加载组件的初始化
            okHttpClientInstance
            retrofitInstance
            api
        } catch (e: Exception) {
            // 预热失败不影响正常使用
            android.util.Log.w("RetrofitInstance", "预热失败: ${e.message}")
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        networkCacheInstance.close()
    }

    companion object {
        @Volatile
        private var INSTANCE: RetrofitInstance? = null

        /**
         * 获取单例实例
         */
        fun getInstance(
            context: Context,
            tokenProvider: TokenProvider? = null
        ): RetrofitInstance {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RetrofitInstance(
                    context.applicationContext,
                    tokenProvider
                ).also { INSTANCE = it }
            }
        }

        /**
         * 重置实例（用于环境切换等场景）
         */
        fun resetInstance() {
            synchronized(this) {
                INSTANCE?.cleanup()
                INSTANCE = null
            }
        }
    }
}